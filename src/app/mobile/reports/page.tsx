"use client";

export default function MobileReportsPage() {
  return (
    <div className="space-y-4">
      <div className="text-center mb-6">
        <h1 className="text-xl font-bold text-gray-900 mb-2">
          รายงานโปรโมชั่น
        </h1>
        <p className="text-sm text-gray-600">
          Promotion Reports - Mobile
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 gap-3 mb-6">
        <div className="bg-white rounded-lg shadow p-4 text-center">
          <h3 className="text-sm font-medium text-blue-900 mb-1">Total Reports</h3>
          <p className="text-xl font-bold text-blue-600">12</p>
          <p className="text-xs text-blue-700">This month</p>
        </div>

        <div className="bg-white rounded-lg shadow p-4 text-center">
          <h3 className="text-sm font-medium text-green-900 mb-1">Downloads</h3>
          <p className="text-xl font-bold text-green-600">45</p>
          <p className="text-xs text-green-700">This week</p>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow p-4">
        <h2 className="text-lg font-semibold mb-3">Generate Reports</h2>
        <div className="space-y-2">
          <button className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors">
            Quick Summary Report
          </button>
          <button className="w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors">
            Application Status Report
          </button>
          <button className="w-full bg-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-purple-700 transition-colors">
            Monthly Analytics
          </button>
        </div>
      </div>

      {/* Recent Reports */}
      <div className="bg-white rounded-lg shadow p-4">
        <h2 className="text-lg font-semibold mb-3">Recent Reports</h2>
        <div className="space-y-3">
          <div className="flex items-center justify-between py-2 border-b border-gray-100">
            <div>
              <p className="text-sm font-medium text-gray-900">Monthly Summary</p>
              <p className="text-xs text-gray-600">Generated today</p>
            </div>
            <button className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded hover:bg-blue-200">
              Download
            </button>
          </div>
          
          <div className="flex items-center justify-between py-2 border-b border-gray-100">
            <div>
              <p className="text-sm font-medium text-gray-900">Application Analytics</p>
              <p className="text-xs text-gray-600">Generated yesterday</p>
            </div>
            <button className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded hover:bg-blue-200">
              Download
            </button>
          </div>
          
          <div className="flex items-center justify-between py-2">
            <div>
              <p className="text-sm font-medium text-gray-900">Weekly Status</p>
              <p className="text-xs text-gray-600">Generated 2 days ago</p>
            </div>
            <button className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded hover:bg-blue-200">
              Download
            </button>
          </div>
        </div>
      </div>

      {/* Report Categories */}
      <div className="bg-white rounded-lg shadow p-4">
        <h2 className="text-lg font-semibold mb-3">Report Categories</h2>
        <div className="grid grid-cols-2 gap-2">
          <button className="bg-gray-50 hover:bg-gray-100 p-3 rounded-lg text-center transition-colors">
            <p className="text-sm font-medium text-gray-900">Summary</p>
            <p className="text-xs text-gray-600">5 reports</p>
          </button>
          
          <button className="bg-gray-50 hover:bg-gray-100 p-3 rounded-lg text-center transition-colors">
            <p className="text-sm font-medium text-gray-900">Analytics</p>
            <p className="text-xs text-gray-600">3 reports</p>
          </button>
          
          <button className="bg-gray-50 hover:bg-gray-100 p-3 rounded-lg text-center transition-colors">
            <p className="text-sm font-medium text-gray-900">Status</p>
            <p className="text-xs text-gray-600">2 reports</p>
          </button>
          
          <button className="bg-gray-50 hover:bg-gray-100 p-3 rounded-lg text-center transition-colors">
            <p className="text-sm font-medium text-gray-900">Custom</p>
            <p className="text-xs text-gray-600">2 reports</p>
          </button>
        </div>
      </div>

      {/* Export Options */}
      <div className="bg-white rounded-lg shadow p-4">
        <h2 className="text-lg font-semibold mb-3">Export Options</h2>
        <div className="space-y-2">
          <button className="w-full bg-gray-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-gray-700 transition-colors">
            Export as PDF
          </button>
          <button className="w-full bg-gray-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-gray-700 transition-colors">
            Export as Excel
          </button>
          <button className="w-full bg-gray-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-gray-700 transition-colors">
            Email Report
          </button>
        </div>
      </div>
    </div>
  );
}

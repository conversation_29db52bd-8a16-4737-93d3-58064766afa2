"use client";

export default function MobilePromotionsPage() {
  return (
    <div className="space-y-4">
      <div className="text-center mb-6">
        <h1 className="text-xl font-bold text-gray-900 mb-2">
          รอบโปรโมชั่น
        </h1>
        <p className="text-sm text-gray-600">
          Promotion Rounds - Mobile
        </p>
      </div>

      {/* Current Round Status */}
      <div className="bg-white rounded-lg shadow p-4">
        <h2 className="text-lg font-semibold mb-3">Current Round</h2>
        <div className="bg-blue-50 p-3 rounded-lg">
          <p className="text-sm font-medium text-blue-900">Round 2024-Q1</p>
          <p className="text-xs text-blue-700">Active until March 31, 2024</p>
          <div className="mt-2">
            <div className="flex justify-between text-xs text-blue-700 mb-1">
              <span>Progress</span>
              <span>75%</span>
            </div>
            <div className="w-full bg-blue-200 rounded-full h-2">
              <div className="bg-blue-600 h-2 rounded-full" style={{ width: '75%' }}></div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 gap-3 mb-6">
        <div className="bg-white rounded-lg shadow p-4 text-center">
          <h3 className="text-sm font-medium text-green-900 mb-1">Applications</h3>
          <p className="text-xl font-bold text-green-600">89</p>
          <p className="text-xs text-green-700">This round</p>
        </div>

        <div className="bg-white rounded-lg shadow p-4 text-center">
          <h3 className="text-sm font-medium text-purple-900 mb-1">Approved</h3>
          <p className="text-xl font-bold text-purple-600">67</p>
          <p className="text-xs text-purple-700">75% rate</p>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow p-4">
        <h2 className="text-lg font-semibold mb-3">Quick Actions</h2>
        <div className="space-y-2">
          <button className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors">
            New Application
          </button>
          <button className="w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors">
            Check Status
          </button>
          <button className="w-full bg-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-purple-700 transition-colors">
            View Guidelines
          </button>
        </div>
      </div>

      {/* Recent Applications */}
      <div className="bg-white rounded-lg shadow p-4">
        <h2 className="text-lg font-semibold mb-3">Recent Applications</h2>
        <div className="space-y-3">
          <div className="flex items-center justify-between py-2 border-b border-gray-100">
            <div>
              <p className="text-sm font-medium text-gray-900">APP-2024-001</p>
              <p className="text-xs text-gray-600">Submitted today</p>
            </div>
            <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">Under Review</span>
          </div>
          
          <div className="flex items-center justify-between py-2 border-b border-gray-100">
            <div>
              <p className="text-sm font-medium text-gray-900">APP-2024-002</p>
              <p className="text-xs text-gray-600">Submitted yesterday</p>
            </div>
            <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Approved</span>
          </div>
          
          <div className="flex items-center justify-between py-2">
            <div>
              <p className="text-sm font-medium text-gray-900">APP-2024-003</p>
              <p className="text-xs text-gray-600">2 days ago</p>
            </div>
            <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">Rejected</span>
          </div>
        </div>
      </div>

      {/* Upcoming Deadlines */}
      <div className="bg-white rounded-lg shadow p-4">
        <h2 className="text-lg font-semibold mb-3">Important Dates</h2>
        <div className="space-y-2">
          <div className="flex justify-between items-center py-2 border-b border-gray-100">
            <span className="text-sm text-gray-900">Application Deadline</span>
            <span className="text-sm font-medium text-red-600">March 15, 2024</span>
          </div>
          <div className="flex justify-between items-center py-2 border-b border-gray-100">
            <span className="text-sm text-gray-900">Review Period Ends</span>
            <span className="text-sm font-medium text-orange-600">March 25, 2024</span>
          </div>
          <div className="flex justify-between items-center py-2">
            <span className="text-sm text-gray-900">Results Announced</span>
            <span className="text-sm font-medium text-green-600">March 31, 2024</span>
          </div>
        </div>
      </div>
    </div>
  );
}

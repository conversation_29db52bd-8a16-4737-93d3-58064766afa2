"use client";

export default function MobileReceivePage() {
  return (
    <div className="space-y-4">
      <div className="text-center mb-6">
        <h1 className="text-xl font-bold text-gray-900 mb-2">
          รับ - Receive
        </h1>
        <p className="text-sm text-gray-600">
          Mobile Interface
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-3 gap-3 mb-6">
        <div className="bg-white rounded-lg shadow p-3 text-center">
          <h3 className="text-xs font-medium text-blue-900 mb-1">New</h3>
          <p className="text-lg font-bold text-blue-600">23</p>
        </div>

        <div className="bg-white rounded-lg shadow p-3 text-center">
          <h3 className="text-xs font-medium text-green-900 mb-1">Processed</h3>
          <p className="text-lg font-bold text-green-600">67</p>
        </div>

        <div className="bg-white rounded-lg shadow p-3 text-center">
          <h3 className="text-xs font-medium text-red-900 mb-1">Urgent</h3>
          <p className="text-lg font-bold text-red-600">3</p>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow p-4">
        <h2 className="text-lg font-semibold mb-3">Quick Actions</h2>
        <div className="space-y-2">
          <button className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors">
            Scan New Document
          </button>
          <button className="w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors">
            Process Pending
          </button>
        </div>
      </div>

      {/* Recent Items */}
      <div className="bg-white rounded-lg shadow p-4">
        <h2 className="text-lg font-semibold mb-3">Recent Items</h2>
        <div className="space-y-3">
          <div className="flex items-center justify-between py-2 border-b border-gray-100">
            <div>
              <p className="text-sm font-medium text-gray-900">Document #R001</p>
              <p className="text-xs text-gray-600">Received 2 hours ago</p>
            </div>
            <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">New</span>
          </div>
          
          <div className="flex items-center justify-between py-2 border-b border-gray-100">
            <div>
              <p className="text-sm font-medium text-gray-900">Document #R002</p>
              <p className="text-xs text-gray-600">Received 4 hours ago</p>
            </div>
            <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">Processing</span>
          </div>
          
          <div className="flex items-center justify-between py-2">
            <div>
              <p className="text-sm font-medium text-gray-900">Document #R003</p>
              <p className="text-xs text-gray-600">Received yesterday</p>
            </div>
            <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Complete</span>
          </div>
        </div>
      </div>

      {/* Search */}
      <div className="bg-white rounded-lg shadow p-4">
        <h2 className="text-lg font-semibold mb-3">Search Documents</h2>
        <div className="space-y-3">
          <input
            type="text"
            placeholder="Search by document number..."
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <button className="w-full bg-gray-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-gray-700 transition-colors">
            Search
          </button>
        </div>
      </div>
    </div>
  );
}

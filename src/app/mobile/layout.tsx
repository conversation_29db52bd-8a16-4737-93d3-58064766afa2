"use client";

import { createTheme, ThemeProvider } from "@design-systems/apollo-ui";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { Home as HomeIcon, Inbox, Calendar, BarChart, Logout as LogoutIcon } from "@design-systems/apollo-icons";

const appTheme = createTheme();

export default function MobileLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const router = useRouter();

  const menuItems = [
    { key: "home", label: "หน้าแรก", icon: <HomeIcon size={20} />, href: "/mobile" },
    { key: "receive", label: "รับ", icon: <Inbox size={20} />, href: "/mobile/receive" },
    { key: "promotions", label: "รอบโปรโมชั่น", icon: <Calendar size={20} />, href: "/mobile/promotions" },
    { key: "reports", label: "รายงานโปรโมชั่น", icon: <BarChart size={20} />, href: "/mobile/reports" },
  ];

  const handleMenuItemClick = (href: string) => {
    router.push(href);
    setIsMenuOpen(false);
  };

  return (
    <ThemeProvider theme={appTheme}>
      <div className="min-h-screen bg-gray-50">
        {/* Mobile Header */}
        <header className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
          <div className="flex items-center justify-between px-4 py-3">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-orange-500 rounded flex items-center justify-center">
                <span className="text-white font-bold text-sm">P</span>
              </div>
              <h1 className="text-lg font-semibold text-gray-900">PMS Mobile</h1>
            </div>
            
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="p-2 rounded-lg hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-orange-500"
              aria-label="Toggle menu"
            >
              <svg
                className="w-6 h-6 text-gray-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                {isMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>

          {/* Mobile Menu Dropdown */}
          {isMenuOpen && (
            <div className="absolute top-full left-0 right-0 bg-white border-t border-gray-200 shadow-lg">
              <nav className="py-2">
                {menuItems.map((item) => (
                  <button
                    key={item.key}
                    onClick={() => handleMenuItemClick(item.href)}
                    className="w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-gray-50 transition-colors"
                  >
                    <span className="text-gray-600">{item.icon}</span>
                    <span className="text-gray-900 font-medium">{item.label}</span>
                  </button>
                ))}
                
                <div className="border-t border-gray-200 mt-2 pt-2">
                  <button
                    onClick={() => console.log("Logout clicked")}
                    className="w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-gray-50 transition-colors"
                  >
                    <span className="text-gray-600">
                      <LogoutIcon size={20} />
                    </span>
                    <span className="text-gray-900 font-medium">ออกจากระบบ</span>
                  </button>
                </div>
              </nav>
            </div>
          )}
        </header>

        {/* Main Content */}
        <main className="p-4">
          {children}
        </main>

        {/* Bottom Navigation */}
        <nav className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2">
          <div className="flex justify-around">
            {menuItems.slice(0, 4).map((item) => (
              <button
                key={item.key}
                onClick={() => handleMenuItemClick(item.href)}
                className="flex flex-col items-center gap-1 py-2 px-3 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <span className="text-gray-600">{item.icon}</span>
                <span className="text-xs text-gray-600 font-medium">{item.label}</span>
              </button>
            ))}
          </div>
        </nav>

        {/* Bottom padding to account for fixed navigation */}
        <div className="h-20"></div>
      </div>
    </ThemeProvider>
  );
}

"use client";

import { DateInput } from "@apollo/ui/legacy";
import { useState } from "react";

export default function MobileHome() {
  const [date, setDate] = useState<Date | null>(null);

  return (
    <div className="space-y-4">
      <div className="text-center mb-6">
        <h1 className="text-xl font-bold text-gray-900 mb-2">
          Welcome to PMS
        </h1>
        <p className="text-sm text-gray-600">
          Mobile Interface
        </p>
      </div>

      {/* Quick Stats Cards */}
      <div className="grid grid-cols-2 gap-3 mb-6">
        <div className="bg-white rounded-lg shadow p-4 text-center">
          <h3 className="text-sm font-medium text-blue-900 mb-1">Total</h3>
          <p className="text-xl font-bold text-blue-600">156</p>
          <p className="text-xs text-blue-700">Applications</p>
        </div>

        <div className="bg-white rounded-lg shadow p-4 text-center">
          <h3 className="text-sm font-medium text-green-900 mb-1">Approved</h3>
          <p className="text-xl font-bold text-green-600">89</p>
          <p className="text-xs text-green-700">57% success</p>
        </div>

        <div className="bg-white rounded-lg shadow p-4 text-center">
          <h3 className="text-sm font-medium text-yellow-900 mb-1">Pending</h3>
          <p className="text-xl font-bold text-yellow-600">45</p>
          <p className="text-xs text-yellow-700">Under review</p>
        </div>

        <div className="bg-white rounded-lg shadow p-4 text-center">
          <h3 className="text-sm font-medium text-purple-900 mb-1">Reports</h3>
          <p className="text-xl font-bold text-purple-600">12</p>
          <p className="text-xs text-purple-700">This month</p>
        </div>
      </div>

      {/* Date Input Section */}
      <div className="bg-white rounded-lg shadow p-4">
        <h2 className="text-lg font-semibold mb-3">Quick Date Selection</h2>
        <DateInput
          value={date}
          onChange={(date) => setDate(date)}
          label="Select Date"
          placeholder="Choose a date"
        />

        {date && (
          <div className="mt-3 p-3 bg-blue-50 rounded">
            <p className="text-blue-800 text-sm">
              Selected: {date.toLocaleDateString()}
            </p>
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow p-4">
        <h2 className="text-lg font-semibold mb-3">Quick Actions</h2>
        <div className="space-y-2">
          <button className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors">
            New Application
          </button>
          <button className="w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors">
            View Pending
          </button>
          <button className="w-full bg-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-purple-700 transition-colors">
            Generate Report
          </button>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-lg shadow p-4">
        <h2 className="text-lg font-semibold mb-3">Recent Activity</h2>
        <div className="space-y-3">
          <div className="flex items-center justify-between py-2 border-b border-gray-100">
            <div>
              <p className="text-sm font-medium text-gray-900">Application #1234</p>
              <p className="text-xs text-gray-600">Submitted today</p>
            </div>
            <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">Pending</span>
          </div>
          
          <div className="flex items-center justify-between py-2 border-b border-gray-100">
            <div>
              <p className="text-sm font-medium text-gray-900">Application #1233</p>
              <p className="text-xs text-gray-600">Yesterday</p>
            </div>
            <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Approved</span>
          </div>
          
          <div className="flex items-center justify-between py-2">
            <div>
              <p className="text-sm font-medium text-gray-900">Report Generated</p>
              <p className="text-xs text-gray-600">2 days ago</p>
            </div>
            <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Complete</span>
          </div>
        </div>
      </div>

      {/* Interface Switch */}
      <div className="bg-white rounded-lg shadow p-4">
        <h2 className="text-lg font-semibold mb-3">Interface Options</h2>
        <p className="text-sm text-gray-600 mb-3">
          Switch between interfaces or return to the main selection page.
        </p>
        <div className="space-y-2">
          <button
            onClick={() => window.location.href = '/'}
            className="w-full bg-gray-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-gray-700 transition-colors"
          >
            ← Back to Home
          </button>
          <button
            onClick={() => window.location.href = '/web'}
            className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors"
          >
            Switch to Web Interface
          </button>
        </div>
      </div>
    </div>
  );
}

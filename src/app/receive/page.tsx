"use client";

export default function ReceivePage() {
  return (
    <div className="max-w-6xl mx-auto">
      <h1 className="text-2xl font-bold text-gray-900 mb-6">
        รับ - Receive Management
      </h1>
      
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <h2 className="text-lg font-semibold mb-4">Receive Dashboard</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-yellow-50 p-4 rounded-lg">
            <h3 className="font-medium text-yellow-900 mb-2">Pending Items</h3>
            <p className="text-2xl font-bold text-yellow-600">24</p>
            <p className="text-sm text-yellow-700">Awaiting processing</p>
          </div>
          
          <div className="bg-green-50 p-4 rounded-lg">
            <h3 className="font-medium text-green-900 mb-2">Processed Today</h3>
            <p className="text-2xl font-bold text-green-600">12</p>
            <p className="text-sm text-green-700">Successfully completed</p>
          </div>
          
          <div className="bg-red-50 p-4 rounded-lg">
            <h3 className="font-medium text-red-900 mb-2">Requires Attention</h3>
            <p className="text-2xl font-bold text-red-600">3</p>
            <p className="text-sm text-red-700">Action needed</p>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold mb-4">Recent Activity</h2>
        <p className="text-gray-600">View recent promotion applications and processing status here.</p>
      </div>
    </div>
  );
}

"use client";

import { DateInput } from "@apollo/ui/legacy";
// import { Button } from "@apollo/ui/legacy";
import { createTheme, ThemeProvider } from "@apollo/ui";
// import "@apollo/ui/style.css"
// import { createTheme, ThemeProvider, DateInput , Tabs, TabsList, Tab, TabPanel, Modal, Button } from "@design-systems/apollo-ui";
import { useState } from "react";

const appTheme = createTheme()

const options = [
  { label: "United States", value: "US" },
  { label: "United Kingdom", value: "UK" },
  { label: "France", value: "FR" },
  { label: "Germany", value: "DE" },
  { label: "Italy", value: "IT" },
]

export default function Home() {
  const [date, setDate] = useState<Date | null>(null)
  const [isOpen, setIsOpen] = useState(false)
  const [selectedOption, setSelectedOption] = useState(null)
  return (
    <ThemeProvider theme={appTheme}>
      {/* <div className="w-full">
        <Tabs defaultValue={0}>
          <TabsList variant="scrollable" scrollButtons>
            <Tab value={0}>Tab 1</Tab>
            <Tab value={1}>Tab 2</Tab>
            <Tab value={2}>Tab 3</Tab>
            <Tab value={3}>Tab 4</Tab>
          </TabsList>

          <TabPanel value={0}>Page for Tab 1</TabPanel>
          <TabPanel value={1}>Page for Tab 2</TabPanel>
          <TabPanel value={2}>Page for Tab 3</TabPanel>
          <TabPanel value={3}>Page for Tab 4</TabPanel>
        </Tabs> */}
        <DateInput
          value={date}
          onChange={(date) => setDate(date)}
          label="Default"
        />
      {/* </div>
      <>
        <Button variant="solid" onClick={() => setIsOpen(true)}>
          Open Negative Modal
        </Button>
        <Modal
          // onOk={() => {
          //   console.log("[Click Event]: I'm okay")
          // }}
          open={isOpen}
          onClose={() => {
            setIsOpen(false)
          }}
          header="ต้องการส่งคำขอโปรโมชั่นนี้อีกครั้ง?"
        >
          <div>
            This modal is specifically
          </div>
        </Modal>
      </>
       <Autocomplete
        options={options}
        label="Country"
        placeholder="Select a country"
        helperText="Choose your country"
        value={selectedOption}
        onChange={(_,option) => {
          console.log(option);
          
          setSelectedOption(option)
        }}
      /> */}
    </ThemeProvider>
  );
}

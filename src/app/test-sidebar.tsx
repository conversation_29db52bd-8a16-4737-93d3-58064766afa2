"use client";

import { createTheme, ThemeProvider } from "@apollo/ui";
import { Sidebar, SidebarLayout, type MenuItemConfig } from "@design-systems/apollo-ui";
import { Home as HomeIcon, Calendar } from "@design-systems/apollo-icons";
import { useState } from "react";

const testTheme = createTheme();

const testMenus: MenuItemConfig[] = [
  {
    key: "test-home",
    label: "Test Home",
    icon: <HomeIcon size={20} />,
    href: "/",
  },
  {
    key: "test-calendar",
    label: "Test Calendar",
    icon: <Calendar size={20} />,
    href: "/calendar",
  },
];

export default function TestSidebar() {
  const [collapsed, setCollapsed] = useState(false);
  const [selectedKey, setSelectedKey] = useState<string | number>("test-home");

  return (
    <ThemeProvider theme={testTheme}>
      <SidebarLayout>
        <Sidebar
          collapsible
          collapsed={collapsed}
          onCollapsedChange={setCollapsed}
          title="Test App"
          menus={testMenus}
          selectedMenuKey={selectedKey}
          onSelectMenu={(key) => setSelectedKey(key)}
        />
        <div className="p-4">
          <h1>Test Sidebar Implementation</h1>
          <p>Selected: {selectedKey}</p>
          <p>Collapsed: {collapsed ? "Yes" : "No"}</p>
        </div>
      </SidebarLayout>
    </ThemeProvider>
  );
}

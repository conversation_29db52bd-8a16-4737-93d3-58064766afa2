"use client";

import { DateInput } from "@apollo/ui/legacy";
import { useState } from "react";

export default function WebUI() {
  const [date, setDate] = useState<Date | null>(null)

  return (
    <div className="max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6 text-gray-900">
        Welcome to PMS Dashboard - Web Interface
      </h1>

      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold mb-4">Date Input Example</h2>
        <DateInput
          value={date}
          onChange={(date) => setDate(date)}
          label="Select Date"
          placeholder="Choose a date"
        />

        {date && (
          <div className="mt-4 p-4 bg-blue-50 rounded">
            <p className="text-blue-800">
              Selected date: {date.toLocaleDateString()}
            </p>
          </div>
        )}
      </div>

      <div className="mt-6 bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold mb-4">Dashboard Overview</h2>
        <p className="text-gray-600">
          Welcome to the Promotion Management System Web Interface. Use the sidebar navigation to access different sections of the application.
        </p>

        <div className="mt-4 p-4 bg-blue-50 rounded">
          <p className="text-blue-800 text-sm">
            <strong>Web Interface Features:</strong> Full sidebar navigation, detailed views, advanced data tables, and comprehensive reporting tools.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-medium text-blue-900 mb-2">Total Applications</h3>
            <p className="text-2xl font-bold text-blue-600">156</p>
            <p className="text-sm text-blue-700">This quarter</p>
          </div>

          <div className="bg-green-50 p-4 rounded-lg">
            <h3 className="font-medium text-green-900 mb-2">Approved</h3>
            <p className="text-2xl font-bold text-green-600">89</p>
            <p className="text-sm text-green-700">57% success rate</p>
          </div>

          <div className="bg-yellow-50 p-4 rounded-lg">
            <h3 className="font-medium text-yellow-900 mb-2">Pending</h3>
            <p className="text-2xl font-bold text-yellow-600">45</p>
            <p className="text-sm text-yellow-700">Under review</p>
          </div>

          <div className="bg-purple-50 p-4 rounded-lg">
            <h3 className="font-medium text-purple-900 mb-2">Reports</h3>
            <p className="text-2xl font-bold text-purple-600">12</p>
            <p className="text-sm text-purple-700">Generated this month</p>
          </div>
        </div>
      </div>

      {/* Interface Switch */}
      <div className="mt-6 bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold mb-4">Interface Options</h2>
        <p className="text-gray-600 mb-4">
          Switch between different interface modes or return to the main selection page.
        </p>
        <div className="flex gap-3">
          <button
            onClick={() => window.location.href = '/'}
            className="bg-gray-600 text-white py-2 px-6 rounded-lg font-medium hover:bg-gray-700 transition-colors"
          >
            ← Back to Home
          </button>
          <button
            onClick={() => window.location.href = '/mobile'}
            className="bg-green-600 text-white py-2 px-6 rounded-lg font-medium hover:bg-green-700 transition-colors"
          >
            Switch to Mobile Interface
          </button>
        </div>
      </div>
    </div>
  );
}

"use client";

import { useState } from "react";

// Option 1: @apollo/ui (newer package)
import {
  createTheme as createThemeNew,
  Theme<PERSON>rovider as ThemeProviderNew
} from "@apollo/ui";
import { DateInput } from "@apollo/ui/legacy";

// Option 2: @design-systems/apollo-ui (older/stable package)
import {
  createTheme,
  ThemeProvider,
  Button,
  Modal
} from "@design-systems/apollo-ui";

// Option 3: @design-systems/apollo-icons (icons package)
import {
  Home as HomeIcon,
  Calendar,
  BarChart
} from "@design-systems/apollo-icons";

export default function Home() {
  const [date, setDate] = useState<Date | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Create themes from both packages
  const oldTheme = createTheme();
  const newTheme = createThemeNew();

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      <h1 className="text-3xl font-bold text-gray-900 mb-8">
        Apollo UI Package Comparison
      </h1>

      {/* Option 1: @apollo/ui */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4 text-blue-600">
          Option 1: @apollo/ui (Newer Package)
        </h2>
        <p className="text-gray-600 mb-4">
          This is the newer Apollo UI package with updated components and modern design patterns.
        </p>

        <ThemeProviderNew theme={newTheme}>
          <div className="space-y-4">
            <div>
              <h3 className="font-medium mb-2">DateInput from @apollo/ui/legacy:</h3>
              <DateInput
                value={date}
                onChange={(date) => setDate(date)}
                label="Select Date (New Package)"
                placeholder="Choose a date"
              />
            </div>

            {date && (
              <div className="p-3 bg-blue-50 rounded">
                <p className="text-blue-800 text-sm">
                  Selected date: {date.toLocaleDateString()}
                </p>
              </div>
            )}
          </div>
        </ThemeProviderNew>
      </div>

      {/* Option 2: @design-systems/apollo-ui */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4 text-green-600">
          Option 2: @design-systems/apollo-ui (Stable Package)
        </h2>
        <p className="text-gray-600 mb-4">
          This is the stable Apollo UI package with comprehensive components including Sidebar, Modal, etc.
        </p>

        <ThemeProvider theme={oldTheme}>
          <div className="space-y-4">
            <div>
              <h3 className="font-medium mb-2">Button and Modal from @design-systems/apollo-ui:</h3>
              <Button
                variant="solid"
                onClick={() => setIsModalOpen(true)}
                className="bg-green-600 hover:bg-green-700"
              >
                Open Modal (Stable Package)
              </Button>

              <Modal
                open={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                header="Modal from @design-systems/apollo-ui"
              >
                <div className="p-4">
                  <p>This modal is from the stable @design-systems/apollo-ui package.</p>
                  <p className="mt-2 text-sm text-gray-600">
                    It includes comprehensive components like Sidebar, navigation, and complex UI elements.
                  </p>
                </div>
              </Modal>
            </div>
          </div>
        </ThemeProvider>
      </div>

      {/* Option 3: @design-systems/apollo-icons */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4 text-purple-600">
          Option 3: @design-systems/apollo-icons (Icons Package)
        </h2>
        <p className="text-gray-600 mb-4">
          This package provides a comprehensive set of icons used across Apollo UI components.
        </p>

        <div className="space-y-4">
          <h3 className="font-medium mb-2">Available Icons:</h3>
          <div className="flex gap-4 items-center">
            <div className="flex items-center gap-2">
              <HomeIcon size={24} className="text-purple-600" />
              <span className="text-sm">HomeIcon</span>
            </div>
            <div className="flex items-center gap-2">
              <Calendar size={24} className="text-purple-600" />
              <span className="text-sm">Calendar</span>
            </div>
            <div className="flex items-center gap-2">
              <BarChart size={24} className="text-purple-600" />
              <span className="text-sm">BarChart</span>
            </div>
          </div>
        </div>
      </div>

      {/* Option 4: Combined Usage */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4 text-orange-600">
          Option 4: Combined @apollo/ui + @design-systems/apollo-ui
        </h2>
        <p className="text-gray-600 mb-4">
          You can use both packages together to leverage the best of both worlds:
        </p>

        <div className="space-y-4">
          <div className="bg-gray-50 p-4 rounded">
            <h3 className="font-medium mb-2">Recommended Approach:</h3>
            <ul className="text-sm text-gray-700 space-y-1">
              <li>• Use <code className="bg-gray-200 px-1 rounded">@apollo/ui</code> for newer, modern components</li>
              <li>• Use <code className="bg-gray-200 px-1 rounded">@design-systems/apollo-ui</code> for stable, complex components like Sidebar</li>
              <li>• Use <code className="bg-gray-200 px-1 rounded">@design-systems/apollo-icons</code> for all icons</li>
              <li>• Import with aliases to avoid naming conflicts (e.g., <code className="bg-gray-200 px-1 rounded">createTheme as createThemeNew</code>)</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Code Examples */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4 text-gray-800">
          Import Examples
        </h2>

        <div className="space-y-4">
          <div>
            <h3 className="font-medium mb-2">Option 1: @apollo/ui only</h3>
            <pre className="bg-gray-100 p-3 rounded text-sm overflow-x-auto">
{`import { createTheme, ThemeProvider } from "@apollo/ui";
import { DateInput } from "@apollo/ui/legacy";`}
            </pre>
          </div>

          <div>
            <h3 className="font-medium mb-2">Option 2: @design-systems/apollo-ui only</h3>
            <pre className="bg-gray-100 p-3 rounded text-sm overflow-x-auto">
{`import {
  createTheme,
  ThemeProvider,
  Sidebar,
  Button,
  Modal
} from "@design-systems/apollo-ui";`}
            </pre>
          </div>

          <div>
            <h3 className="font-medium mb-2">Option 3: Icons only</h3>
            <pre className="bg-gray-100 p-3 rounded text-sm overflow-x-auto">
{`import {
  Home,
  Calendar,
  BarChart,
  Inbox
} from "@design-systems/apollo-icons";`}
            </pre>
          </div>

          <div>
            <h3 className="font-medium mb-2">Option 4: Combined (Recommended)</h3>
            <pre className="bg-gray-100 p-3 rounded text-sm overflow-x-auto">
{`// Modern components
import {
  createTheme as createThemeNew,
  ThemeProvider as ThemeProviderNew
} from "@apollo/ui";
import { DateInput } from "@apollo/ui/legacy";

// Stable complex components
import {
  createTheme,
  ThemeProvider,
  Sidebar,
  Button,
  Modal
} from "@design-systems/apollo-ui";

// Icons
import {
  Home,
  Calendar,
  BarChart
} from "@design-systems/apollo-icons";`}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
}

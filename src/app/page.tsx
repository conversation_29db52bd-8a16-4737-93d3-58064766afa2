"use client";

import { useState } from "react";

// Option 1: @apollo/ui (newer package)
import {
  createTheme as createThemeNew,
  <PERSON><PERSON><PERSON><PERSON> as ThemeProviderNew
} from "@apollo/ui";
import { DateInput } from "@apollo/ui/legacy";

// Option 2: @design-systems/apollo-ui (older/stable package)
import {
  createTheme,
  ThemeProvider,
  Button,
  Modal
} from "@design-systems/apollo-ui";

// Option 3: @design-systems/apollo-icons (icons package)
import {
  Home as HomeIcon,
  Calendar,
  BarChart,
  Inbox,
  Logout as LogoutIcon
} from "@design-systems/apollo-icons";

type PackageOption = '@apollo/ui' | '@design-systems/apollo-ui' | 'combined';

export default function Home() {
  const [selectedPackage, setSelectedPackage] = useState<PackageOption>('@apollo/ui');
  const [date, setDate] = useState<Date | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Create themes from both packages
  const oldTheme = createTheme();
  const newTheme = createThemeNew();

  const renderApolloUIContent = () => (
    <div className="space-y-6">
      <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-6">
        <h2 className="text-2xl font-semibold mb-4 text-blue-800">
          @apollo/ui Package
        </h2>
        <p className="text-blue-700 mb-6">
          Modern Apollo UI package with updated components and contemporary design patterns.
        </p>

        <ThemeProviderNew theme={newTheme}>
          <div className="space-y-6">
            <div className="bg-white rounded-lg p-4">
              <h3 className="font-semibold mb-3 text-gray-800">DateInput Component</h3>
              <DateInput
                value={date}
                onChange={(date) => setDate(date)}
                label="Select Date (@apollo/ui)"
                placeholder="Choose a date"
              />

              {date && (
                <div className="mt-4 p-3 bg-blue-50 rounded border-l-4 border-blue-400">
                  <p className="text-blue-800 font-medium">
                    Selected: {date.toLocaleDateString()}
                  </p>
                </div>
              )}
            </div>

            <div className="bg-white rounded-lg p-4">
              <h3 className="font-semibold mb-3 text-gray-800">Package Features</h3>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                  Modern component architecture
                </li>
                <li className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                  Updated design tokens
                </li>
                <li className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                  Legacy component support
                </li>
                <li className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                  Enhanced theming system
                </li>
              </ul>
            </div>
          </div>
        </ThemeProviderNew>
      </div>
    </div>
  );

  const renderDesignSystemsContent = () => (
    <div className="space-y-6">
      <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-6">
        <h2 className="text-2xl font-semibold mb-4 text-green-800">
          @design-systems/apollo-ui Package
        </h2>
        <p className="text-green-700 mb-6">
          Stable and comprehensive Apollo UI package with mature components and extensive functionality.
        </p>

        <ThemeProvider theme={oldTheme}>
          <div className="space-y-6">
            <div className="bg-white rounded-lg p-4">
              <h3 className="font-semibold mb-3 text-gray-800">Button & Modal Components</h3>
              <Button
                variant="solid"
                onClick={() => setIsModalOpen(true)}
                className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg"
              >
                Open Modal (@design-systems/apollo-ui)
              </Button>

              <Modal
                open={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                header="Design Systems Modal"
              >
                <div className="p-4">
                  <p className="text-gray-700 mb-3">
                    This modal is from the stable @design-systems/apollo-ui package.
                  </p>
                  <p className="text-sm text-gray-600">
                    This package includes comprehensive components like Sidebar, complex navigation,
                    and enterprise-ready UI elements.
                  </p>
                </div>
              </Modal>
            </div>

            <div className="bg-white rounded-lg p-4">
              <h3 className="font-semibold mb-3 text-gray-800">Package Features</h3>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  Comprehensive component library
                </li>
                <li className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  Stable and battle-tested
                </li>
                <li className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  Complex UI components (Sidebar, Navigation)
                </li>
                <li className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  Enterprise-ready features
                </li>
              </ul>
            </div>

            <div className="bg-white rounded-lg p-4">
              <h3 className="font-semibold mb-3 text-gray-800">Available Icons</h3>
              <div className="flex gap-6 items-center flex-wrap">
                <div className="flex items-center gap-2">
                  <HomeIcon size={24} className="text-green-600" />
                  <span className="text-sm text-gray-700">Home</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar size={24} className="text-green-600" />
                  <span className="text-sm text-gray-700">Calendar</span>
                </div>
                <div className="flex items-center gap-2">
                  <BarChart size={24} className="text-green-600" />
                  <span className="text-sm text-gray-700">BarChart</span>
                </div>
                <div className="flex items-center gap-2">
                  <Inbox size={24} className="text-green-600" />
                  <span className="text-sm text-gray-700">Inbox</span>
                </div>
                <div className="flex items-center gap-2">
                  <LogoutIcon size={24} className="text-green-600" />
                  <span className="text-sm text-gray-700">Logout</span>
                </div>
              </div>
            </div>
          </div>
        </ThemeProvider>
      </div>
    </div>
  );

  const renderCombinedContent = () => (
    <div className="space-y-6">
      <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-6">
        <h2 className="text-2xl font-semibold mb-4 text-purple-800">
          Combined: @apollo/ui + @design-systems/apollo-ui
        </h2>
        <p className="text-purple-700 mb-6">
          Best of both worlds - modern components from @apollo/ui with stable components from @design-systems/apollo-ui.
        </p>

        <div className="space-y-6">
          {/* Modern components section */}
          <div className="bg-white rounded-lg p-4 border-l-4 border-blue-400">
            <h3 className="font-semibold mb-3 text-gray-800">Modern Components (@apollo/ui)</h3>
            <ThemeProviderNew theme={newTheme}>
              <DateInput
                value={date}
                onChange={(date) => setDate(date)}
                label="Modern DateInput"
                placeholder="From @apollo/ui"
              />

              {date && (
                <div className="mt-3 p-3 bg-blue-50 rounded">
                  <p className="text-blue-800 text-sm font-medium">
                    Date from modern package: {date.toLocaleDateString()}
                  </p>
                </div>
              )}
            </ThemeProviderNew>
          </div>

          {/* Stable components section */}
          <div className="bg-white rounded-lg p-4 border-l-4 border-green-400">
            <h3 className="font-semibold mb-3 text-gray-800">Stable Components (@design-systems/apollo-ui)</h3>
            <ThemeProvider theme={oldTheme}>
              <Button
                variant="solid"
                onClick={() => setIsModalOpen(true)}
                className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg"
              >
                Open Stable Modal
              </Button>

              <Modal
                open={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                header="Combined Package Demo"
              >
                <div className="p-4">
                  <p className="text-gray-700 mb-3">
                    This demonstrates using both packages together effectively.
                  </p>
                  <div className="bg-gray-50 p-3 rounded">
                    <p className="text-sm text-gray-600 mb-2">
                      <strong>Strategy:</strong>
                    </p>
                    <ul className="text-xs text-gray-600 space-y-1">
                      <li>• Use @apollo/ui for modern, updated components</li>
                      <li>• Use @design-systems/apollo-ui for complex, stable components</li>
                      <li>• Import with aliases to avoid conflicts</li>
                      <li>• Leverage the best features from each package</li>
                    </ul>
                  </div>
                </div>
              </Modal>
            </ThemeProvider>
          </div>

          {/* Icons section */}
          <div className="bg-white rounded-lg p-4 border-l-4 border-purple-400">
            <h3 className="font-semibold mb-3 text-gray-800">Icons (@design-systems/apollo-icons)</h3>
            <div className="grid grid-cols-5 gap-4">
              <div className="text-center">
                <HomeIcon size={32} className="text-purple-600 mx-auto mb-1" />
                <span className="text-xs text-gray-600">Home</span>
              </div>
              <div className="text-center">
                <Calendar size={32} className="text-purple-600 mx-auto mb-1" />
                <span className="text-xs text-gray-600">Calendar</span>
              </div>
              <div className="text-center">
                <BarChart size={32} className="text-purple-600 mx-auto mb-1" />
                <span className="text-xs text-gray-600">BarChart</span>
              </div>
              <div className="text-center">
                <Inbox size={32} className="text-purple-600 mx-auto mb-1" />
                <span className="text-xs text-gray-600">Inbox</span>
              </div>
              <div className="text-center">
                <LogoutIcon size={32} className="text-purple-600 mx-auto mb-1" />
                <span className="text-xs text-gray-600">Logout</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header with Selection */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Apollo UI Package Explorer
          </h1>
          <p className="text-gray-600 mb-6">
            Select a package option to see live examples and components in action.
          </p>

          {/* Package Selection Dropdown */}
          <div className="flex items-center gap-4">
            <label htmlFor="package-select" className="text-sm font-medium text-gray-700">
              Choose Apollo UI Package:
            </label>
            <select
              id="package-select"
              value={selectedPackage}
              onChange={(e) => setSelectedPackage(e.target.value as PackageOption)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900 min-w-[300px]"
            >
              <option value="@apollo/ui">@apollo/ui (Modern Package)</option>
              <option value="@design-systems/apollo-ui">@design-systems/apollo-ui (Stable Package)</option>
              <option value="combined">Combined Approach (Recommended)</option>
            </select>
          </div>
        </div>

        {/* Dynamic Content Based on Selection */}
        <div className="transition-all duration-300 ease-in-out">
          {selectedPackage === '@apollo/ui' && renderApolloUIContent()}
          {selectedPackage === '@design-systems/apollo-ui' && renderDesignSystemsContent()}
          {selectedPackage === 'combined' && renderCombinedContent()}
        </div>

        {/* Code Examples Section */}
        <div className="mt-8 bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold mb-4 text-gray-800">
            Import Code for {selectedPackage}
          </h2>

          <div className="bg-gray-100 rounded-lg p-4 overflow-x-auto">
            <pre className="text-sm text-gray-800">
              {selectedPackage === '@apollo/ui' && (
`import { createTheme, ThemeProvider } from "@apollo/ui";
import { DateInput } from "@apollo/ui/legacy";

const theme = createTheme();

<ThemeProvider theme={theme}>
  <DateInput
    value={date}
    onChange={setDate}
    label="Select Date"
  />
</ThemeProvider>`
              )}

              {selectedPackage === '@design-systems/apollo-ui' && (
`import {
  createTheme,
  ThemeProvider,
  Button,
  Modal
} from "@design-systems/apollo-ui";
import {
  Home,
  Calendar,
  BarChart
} from "@design-systems/apollo-icons";

const theme = createTheme();

<ThemeProvider theme={theme}>
  <Button onClick={openModal}>Open Modal</Button>
  <Modal open={isOpen} onClose={closeModal}>
    Content here
  </Modal>
</ThemeProvider>`
              )}

              {selectedPackage === 'combined' && (
`// Modern components
import {
  createTheme as createThemeNew,
  ThemeProvider as ThemeProviderNew
} from "@apollo/ui";
import { DateInput } from "@apollo/ui/legacy";

// Stable components
import {
  createTheme,
  ThemeProvider,
  Button,
  Modal
} from "@design-systems/apollo-ui";

// Icons
import { Home, Calendar } from "@design-systems/apollo-icons";

// Use both themes
const newTheme = createThemeNew();
const stableTheme = createTheme();

// Modern components
<ThemeProviderNew theme={newTheme}>
  <DateInput value={date} onChange={setDate} />
</ThemeProviderNew>

// Stable components
<ThemeProvider theme={stableTheme}>
  <Button>Stable Button</Button>
</ThemeProvider>`
              )}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
}
"use client";

import { useRouter } from "next/navigation";
import { Home as HomeIcon, BarC<PERSON> } from "@design-systems/apollo-icons";

export default function Home() {
  const router = useRouter();

  const handleWebUIClick = () => {
    router.push("/web");
  };

  const handleMobileUIClick = () => {
    router.push("/mobile");
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="max-w-4xl mx-auto px-6">
        {/* Header Section */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-6">
            <div className="w-16 h-16 bg-orange-500 rounded-xl flex items-center justify-center shadow-lg">
              <span className="text-white font-bold text-2xl">P</span>
            </div>
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Welcome to PMS
          </h1>
          <p className="text-xl text-gray-600 mb-2">
            Promotion Management System
          </p>
          <p className="text-lg text-gray-500">
            Choose your preferred interface to get started
          </p>
        </div>

        {/* UI Selection Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
          {/* Web UI Card */}
          <button
            onClick={handleWebUIClick}
            className="group bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer border-2 border-transparent hover:border-blue-200 transform hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 text-left"
            aria-label="Navigate to Web UI interface"
          >
            <div className="p-8 text-center">
              <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-blue-200 transition-colors duration-300">
                <HomeIcon size={40} className="text-blue-600" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Web UI</h2>
              <p className="text-gray-600 mb-6 leading-relaxed">
                Full-featured desktop interface with comprehensive navigation, detailed views, and advanced functionality for power users.
              </p>
              <div className="bg-blue-50 rounded-lg p-4 mb-6">
                <p className="text-blue-800 text-sm font-medium">
                  ✓ Complete sidebar navigation<br/>
                  ✓ Multi-column layouts<br/>
                  ✓ Advanced data tables<br/>
                  ✓ Detailed reporting tools
                </p>
              </div>
              <div className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200 group-hover:bg-blue-700">
                Enter Web Interface
              </div>
            </div>
          </button>

          {/* Mobile UI Card */}
          <button
            onClick={handleMobileUIClick}
            className="group bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer border-2 border-transparent hover:border-green-200 transform hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 text-left"
            aria-label="Navigate to Mobile UI interface"
          >
            <div className="p-8 text-center">
              <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-green-200 transition-colors duration-300">
                <BarChart size={40} className="text-green-600" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Mobile UI</h2>
              <p className="text-gray-600 mb-6 leading-relaxed">
                Touch-optimized mobile interface designed for on-the-go access with simplified navigation and essential features.
              </p>
              <div className="bg-green-50 rounded-lg p-4 mb-6">
                <p className="text-green-800 text-sm font-medium">
                  ✓ Touch-friendly design<br/>
                  ✓ Simplified navigation<br/>
                  ✓ Quick actions<br/>
                  ✓ Mobile-optimized forms
                </p>
              </div>
              <div className="w-full bg-green-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-green-700 transition-colors duration-200 group-hover:bg-green-700">
                Enter Mobile Interface
              </div>
            </div>
          </button>
        </div>

        {/* Footer Info */}
        <div className="text-center">
          <p className="text-gray-500 text-sm">
            You can switch between interfaces at any time from within the application
          </p>
        </div>
      </div>
    </div>
  );
}

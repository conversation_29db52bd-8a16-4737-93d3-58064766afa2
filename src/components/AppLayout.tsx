"use client";

import {
  createTheme, ThemeProvider,
  Sidebar,
  type MenuItemConfig,
  type MenuSectionConfig
} from "@design-systems/apollo-ui";
import {
  Home as HomeIcon,
  Inbox,
  Calendar,
  BarChart,
  Logout as LogoutIcon
} from "@design-systems/apollo-icons";
import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";

const appTheme = createTheme()

// Define sidebar menu configuration
const sidebarMenus: (MenuSectionConfig | MenuItemConfig)[] = [
  {
    key: "main-section",
    label: "แฟ้มหลัก", // Main Files
    items: [
      {
        key: "home",
        label: "หน้าแรก", // Home
        icon: <HomeIcon size={20} />,
        href: "/",
      },
      {
        key: "receive",
        label: "รับ", // Receive
        icon: <Inbox size={20} />,
        href: "/receive",
      },
      {
        key: "promotions",
        label: "รอบโปรโมชั่น", // Promotion Rounds
        icon: <Calendar size={20} />,
        href: "/promotions",
      },
      {
        key: "reports",
        label: "รายงานโปรโมชั่น", // Promotion Reports
        icon: <BarChart size={20} />,
        href: "/reports",
      },
    ],
  },
  // {
  //   key: "other-section",
  //   label: "อื่นๆ", // Others
  //   items: [
  //     {
  //       key: "documents",
  //       label: "รายงาน", // Reports
  //       icon: <FileText size={20} />,
  //       href: "/documents",
  //       children: [
  //         {
  //           key: "monthly-reports",
  //           label: "รายงานรายเดือน", // Monthly Reports
  //           href: "/documents/monthly",
  //         },
  //         {
  //           key: "yearly-reports",
  //           label: "รายงานรายปี", // Yearly Reports
  //           href: "/documents/yearly",
  //         },
  //       ],
  //     },
  //   ],
  // },
];

export default function Layout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname()
  const [sidebarCollapsed, setSidebarCollapsed] = useState<boolean>(false)
  const [selectedMenuKey, setSelectedMenuKey] = useState<string | number>("home")
  // const [expandedMenuKeys, setExpandedMenuKeys] = useState<Array<string | number>>([])

  // Handle client-side mounting and restore sidebar state
  useEffect(() => {
    // Restore sidebar collapsed state from localStorage (only on client-side)
    if (typeof window !== 'undefined') {
      const savedCollapsedState = localStorage.getItem('sidebar-collapsed')
      if (savedCollapsedState !== null) {
        setSidebarCollapsed(JSON.parse(savedCollapsedState))
      }
    }
  }, [sidebarCollapsed])

  // Update selected menu key when pathname changes
  useEffect(() => {
    const menuKey = pathname.replace("/", "") || "home"
    setSelectedMenuKey(menuKey)

    // Auto-expand documents menu if on a documents sub-page
    // if (pathname.startsWith("/documents/")) {
    //   setExpandedMenuKeys(prev => prev.includes("documents") ? prev : [...prev, "documents"])
    // }

    console.log("🔄 Route changed to:", pathname, "→ Menu key:", menuKey)
  }, [pathname])


  // Handle sidebar collapse/expand and persist state
  const handleSidebarCollapse = (collapsed: boolean) => {
    setSidebarCollapsed(collapsed)
    // Save to localStorage to persist across page navigation (only on client-side)
    if (typeof window !== 'undefined') {
      localStorage.setItem('sidebar-collapsed', JSON.stringify(collapsed))
    }
    console.log("🔧 Sidebar", collapsed ? "collapsed" : "expanded")
  }

  return (
    <ThemeProvider theme={appTheme}>
      <div className="flex h-screen">
        {/* eslint-disable-next-line react/jsx-key, react/jsx-props-no-spreading */}
        {/* Apollo UI Sidebar component spreads props containing 'key' internally - library limitation */}
        <Sidebar
          collapsible
          collapsed={sidebarCollapsed}
          onCollapsedChange={handleSidebarCollapse}
          title="PMS"
          logo={
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-orange-500 rounded flex items-center justify-center">
                <span className="text-white font-bold text-sm">P</span>
              </div>
            </div>
          }
          menus={sidebarMenus}
          selectedMenuKey={selectedMenuKey}
          // expandedMenuKeys={expandedMenuKeys}
          footer={[
            {
              key: "logout",
              label: "ออกจากระบบ", // Logout
              icon: <LogoutIcon size={20} />,
              onClick: () => console.log("Logout clicked"),
            },
          ]}
        />

        {/* Page content will go here */}
        <div className="flex-1 p-6 bg-gray-50 overflow-auto">
          {children}
        </div>
      </div>
    </ThemeProvider>
  );
}
